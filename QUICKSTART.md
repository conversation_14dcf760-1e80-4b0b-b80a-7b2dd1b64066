# 快速开始

## 🚀 一键启动

### 方法一：交互式启动（推荐）

```bash
./start.sh
# 根据提示选择环境：开发/预发/生产
```

### 方法二：直接启动特定环境

```bash
# 开发环境
./start-dev.sh

# 预发环境
./start-staging.sh

# 生产环境
./start-prod.sh
```

### 方法二：手动启动

#### 1. 确保环境准备就绪

- ✅ Node.js >= 16
- ✅ MongoDB 已启动
- ✅ npm >= 8

#### 2. 安装依赖

```bash
# 后端依赖
cd server
npm install

# 前端依赖
cd ../admin
npm install
```

#### 3. 配置环境

```bash
cd server
cp .env.example .env
# 根据需要修改 .env 文件中的配置
```

#### 4. 启动服务

```bash
# 终端1: 启动后端服务
cd server
npm run start:dev

# 终端2: 启动前端服务
cd admin
npm start
```

## 🌐 访问地址

启动成功后，您可以访问：

- **前端管理平台**: http://localhost:3001
- **后端 API**: http://localhost:3000
- **API 文档**: http://localhost:3000/api

## 📱 功能演示

### 1. 登录系统

1. 访问管理平台：http://localhost:3001
2. 使用默认账户登录：
   - 用户名：admin
   - 密码：admin

### 2. 管理商品

- 查看商品列表
- 创建新商品
- 编辑商品信息
- 查看商品详情

### 3. 更改密码

1. 登录后点击侧边栏"更改密码"
2. 输入当前密码和新密码
3. 确认更改

### 4. API 测试

```bash
# 登录获取 token
curl -X POST http://localhost:3000/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin"
  }'

# 使用 token 访问商品列表
curl -X GET http://localhost:3000/products \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔧 常见问题

### MongoDB 连接失败

```bash
# macOS
brew services start mongodb-community

# Ubuntu
sudo systemctl start mongod

# Windows
net start MongoDB
```

### 端口被占用

- 后端默认端口：3000
- 前端默认端口：3001

如需修改端口，请编辑相应的配置文件。

### 依赖安装失败

```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

## 📚 更多文档

- [API 文档](docs/API.md)
- [部署指南](docs/DEPLOYMENT.md)
- [项目说明](README.md)

## 🎯 下一步

1. 登录管理平台
2. 创建您的第一个商品
3. 查看商品统计数据
4. 更改默认密码
5. 根据需要自定义功能

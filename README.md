# 商品管理平台

一个基于 NestJS + React 的商品管理系统，支持用户认证和权限管理。

## 功能特性

### 管理平台功能

- 用户登录认证
- 商品管理页面
- 创建商品功能
- 商品列表展示
- 商品详情查看
- 商品信息编辑
- 商品分页显示
- 更改密码功能
- **微信小程序二维码生成**
- **二维码预览和下载**

### API 功能

- 用户认证接口
- 商品管理接口
- JWT 权限验证
- **微信小程序二维码生成**
- **小程序端商品信息获取**

## 技术栈

### 后端 (server)

- NodeJS + NestJS + TypeScript
- MongoDB 数据库
- JWT 认证
- Swagger API 文档

### 前端 (admin)

- React + TypeScript
- Ant Design UI 组件库
- Axios HTTP 客户端

## 项目结构

```
qr-management-platform/
├── server/          # NestJS 后端服务
├── admin/           # React 前端管理平台
├── docs/            # 项目文档
└── README.md        # 项目说明
```

## 快速开始

### 方式一：一键启动（推荐）

```bash
# 交互式选择环境
./start.sh

# 或直接启动开发环境
./start-dev.sh
```

### 方式二：手动启动

```bash
# 1. 启动后端服务
cd server
npm install
npm run start:dev

# 2. 启动前端管理平台
cd admin
npm install
npm start
```

### 3. 访问应用

- 前端管理平台: http://localhost:3001
- 后端 API: http://localhost:3000
- API 文档: http://localhost:3000/api

## 多环境支持

本项目支持三种环境配置：

| 环境     | 启动命令             | 用途     | 数据库                |
| -------- | -------------------- | -------- | --------------------- |
| 开发环境 | `./start-dev.sh`     | 本地开发 | qr_management_dev     |
| 预发环境 | `./start-staging.sh` | 测试部署 | qr_management_staging |
| 生产环境 | `./start-prod.sh`    | 正式部署 | qr_management_prod    |

详细配置请参考 [环境配置指南](docs/ENVIRONMENTS.md)

## 环境要求

- Node.js >= 16
- MongoDB >= 4.4
- npm >= 8

## 开发说明

### 数据库配置

请确保 MongoDB 服务已启动，默认连接地址为 `mongodb://*************:27017/qr_management`

### 环境变量

后端服务需要配置 `.env` 文件，参考 `server/.env.example`

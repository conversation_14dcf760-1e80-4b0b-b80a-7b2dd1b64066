# 微信小程序相关 API 文档

## 1. 小程序端验证商品二维码

### 接口地址

```
POST /verification/wechat/verify
```

### 功能描述

用于在微信小程序端通过扫描二维码验证商品信息，并记录验证行为。

### 请求参数

| 参数名        | 类型   | 必填 | 描述                          |
| ------------- | ------ | ---- | ----------------------------- |
| productNumber | string | 是   | 商品编号（8 位数字）          |
| phoneNumber   | string | 否   | 验证人手机号                  |
| userName      | string | 否   | 验证人姓名                    |
| wechatOpenId  | string | 否   | 微信用户 OpenID               |
| wechatUnionId | string | 否   | 微信用户 UnionID              |
| location      | object | 否   | 验证地点信息（包含经纬度等）  |
| deviceInfo    | object | 否   | 设备信息（如 User-Agent、IP） |

### 示例请求体

```json
{
  "productNumber": "12345678",
  "phoneNumber": "13800138000",
  "userName": "张三",
  "location": {
    "latitude": 39.9042,
    "longitude": 116.4074,
    "address": "北京市"
  },
  "deviceInfo": {
    "userAgent": "WeChat Client",
    "ip": "***********"
  }
}
```

### 响应示例 (成功)

```json
{
  "success": true,
  "data": {
    "isVerified": true,
    "verificationTime": "2024-01-20T10:30:00.000Z",
    "verifiedBy": "13800138000",
    "verificationId": "60f7b3b3b3b3b3b3b3b3b3b3"
  }
}
```

### 响应状态码

| 状态码 | 描述                   |
| ------ | ---------------------- |
| 200    | 成功                   |
| 400    | 请求参数错误或商品异常 |
| 404    | 商品不存在             |
| 409    | 商品已被验证           |

---

## 2. 获取商品详情（包含验证状态）

### 接口地址

```
GET /wechat/product/{productNumber}
```

### 功能描述

根据商品编号获取商品详细信息以及该商品是否已经被验证的状态信息。

### 路径参数

| 参数名        | 类型   | 必填 | 描述                 |
| ------------- | ------ | ---- | -------------------- |
| productNumber | string | 是   | 商品编号（8 位数字） |

### 示例响应 (已验证商品)

```json
{
  "success": true,
  "data": {
    "product": {
      "productNumber": "12345678",
      "name": "茅台酒",
      "alcoholContent": 53,
      "packagingDate": "2024-01-15T00:00:00.000Z",
      "brand": "茅台",
      "category": "白酒",
      "volume": 500,
      "description": "优质白酒",
      "status": "active",
      "batchNumber": "B20240115001",
      "productionLocation": "贵州茅台镇"
    },
    "verification": {
      "isVerified": true,
      "verificationTime": "2024-01-20T10:30:00.000Z",
      "verifiedBy": "13800138000",
      "verificationId": "60f7b3b3b3b3b3b3b3b3b3b3"
    }
  }
}
```

### 示例响应 (未验证商品)

```json
{
  "success": true,
  "data": {
    "product": {
      "productNumber": "87654321",
      "name": "五粮液",
      "alcoholContent": 52,
      "packagingDate": "2024-01-10T00:00:00.000Z",
      "brand": "五粮液",
      "category": "白酒",
      "volume": 500,
      "description": "优质白酒",
      "status": "active",
      "batchNumber": "B20240110001",
      "productionLocation": "四川宜宾"
    },
    "verification": {
      "isVerified": false,
      "verificationTime": null,
      "verifiedBy": null,
      "verificationId": null
    }
  }
}
```

### 响应状态码

| 状态码 | 描述       |
| ------ | ---------- |
| 200    | 成功       |
| 404    | 商品不存在 |

---

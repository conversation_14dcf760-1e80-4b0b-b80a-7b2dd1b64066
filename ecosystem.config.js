module.exports = {
  apps: [
    {
      name: 'qr-backend-dev',
      script: 'server/dist/main.js',
      cwd: './',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      env_file: 'server/.env.development',
      log_file: 'logs/dev/combined.log',
      out_file: 'logs/dev/out.log',
      error_file: 'logs/dev/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      watch: true,
      ignore_watch: ['node_modules', 'logs'],
      max_memory_restart: '500M'
    },
    {
      name: 'qr-backend-staging',
      script: 'server/dist/main.js',
      cwd: './',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'staging',
        PORT: 3000
      },
      env_file: 'server/.env.staging',
      log_file: 'logs/staging/combined.log',
      out_file: 'logs/staging/out.log',
      error_file: 'logs/staging/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      watch: false,
      max_memory_restart: '1G',
      min_uptime: '10s',
      max_restarts: 10
    },
    {
      name: 'qr-backend-prod',
      script: 'server/dist/main.js',
      cwd: './',
      instances: 'max', // 使用所有 CPU 核心
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_file: 'server/.env.production',
      log_file: 'logs/prod/combined.log',
      out_file: 'logs/prod/out.log',
      error_file: 'logs/prod/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      watch: false,
      max_memory_restart: '2G',
      min_uptime: '60s',
      max_restarts: 5,
      restart_delay: 4000,
      // 健康检查
      health_check_grace_period: 3000,
      // 优雅关闭
      kill_timeout: 5000,
      listen_timeout: 8000,
      // 自动重启策略
      autorestart: true,
      // 错误重启延迟
      exp_backoff_restart_delay: 100
    }
  ],

  deploy: {
    staging: {
      user: 'deploy',
      host: 'staging.your-domain.com',
      ref: 'origin/develop',
      repo: '**************:your-username/qr-management-platform.git',
      path: '/var/www/qr-management-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build:staging && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': ''
    },
    production: {
      user: 'deploy',
      host: 'your-domain.com',
      ref: 'origin/main',
      repo: '**************:your-username/qr-management-platform.git',
      path: '/var/www/qr-management-prod',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && npm run build:prod && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};

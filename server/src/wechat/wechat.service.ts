import { Injectable, Logger, NotFoundException } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";
import { Inject } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { firstValueFrom } from "rxjs";
import { AxiosResponse } from "axios";
import { Product, ProductDocument } from "../product/schemas/product.schema";
import {
  Verification,
  VerificationDocument,
} from "../verification/schemas/verification.schema";
import { DateFormatterUtil } from "../common/utils/date-formatter.util";

@Injectable()
export class WechatService {
  private readonly logger = new Logger(WechatService.name);
  private readonly tokenKey = "wechat_access_token";

  constructor(
    private readonly httpService: HttpService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
    @InjectModel(Verification.name)
    private verificationModel: Model<VerificationDocument>
  ) {}

  async getAccessToken(): Promise<string> {
    const cachedToken = await this.cacheManager.get(this.tokenKey);
    if (cachedToken) {
      this.logger.log("Using cached access token");
      return cachedToken as string;
    }

    const appId = process.env.WECHAT_APP_ID;
    const appSecret = process.env.WECHAT_APP_SECRET;
    const url = `https://api.weixin.qq.com/cgi-bin/stable_token`;

    try {
      const response = await firstValueFrom(
        this.httpService.post<{ access_token: string; expires_in: number }>(
          url,
          {
            grant_type: "client_credential",
            appid: appId,
            secret: appSecret,
          },
          {
            headers: { "Content-Type": "application/json" },
          }
        )
      );

      const token = response.data.access_token;
      const expiresIn = response.data.expires_in;

      // 缓存token，有效期设置为7200秒
      await this.cacheManager.set(this.tokenKey, token, expiresIn - 60);
      this.logger.log(`New access token obtained and cached ${token}`);

      return token;
    } catch (error) {
      this.logger.error("Failed to get access token", error.stack);
      throw new Error("WECHAT_TOKEN_FAILURE");
    }
  }

  async generateWxacode(params: {
    page: string;
    scene: string;
    check_path?: boolean;
    env_version?: string;
  }): Promise<Buffer> {
    const accessToken = await this.getAccessToken();

    // const url = `https://api.weixin.qq.com/wxa/getwxacode?access_token=${accessToken}`;
    const url = `https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=${accessToken}`;

    try {
      if (!accessToken) {
        throw new Error("WECHAT_TOKEN_FAILURE");
      }

      const response = await firstValueFrom(
        this.httpService.post(url, params, {
          responseType: "arraybuffer",
          headers: { "Content-Type": "application/json" },
        })
      );

      // 检查响应是否是错误
      const result = response.data.toString();
      if (result.includes("errcode")) {
        const errorData = JSON.parse(result);
        this.logger.error(`Wechat API error: ${JSON.stringify(errorData)}`);
        throw new Error("WECHAT_API_ERROR");
      }

      return response.data;
    } catch (error) {
      this.logger.error("Failed to generate wxacode", error.stack);
      throw new Error("QRCODE_GENERATION_FAILED");
    }
  }

  /**
   * 获取商品详情（包含验证状态）
   * @param productNumber 商品编号
   * @returns 商品信息和验证状态
   */
  async getProductDetail(productNumber: string) {
    // 验证商品编号格式
    if (!/^\d{8}$/.test(productNumber)) {
      throw new NotFoundException("商品编号格式错误，必须是8位数字");
    }

    try {
      // 1. 获取商品信息
      const product = await this.productModel.findOne({ productNumber }).exec();
      if (!product) {
        throw new NotFoundException("商品不存在");
      }

      // 2. 获取验证状态 - 直接通过 verifiedAt 判断
      let verificationInfo = {
        isVerified: !!product.verifiedAt, // 转换为布尔值
        verificationTime: product.verifiedAt
          ? DateFormatterUtil.formatDateTime(product.verifiedAt)
          : null,
        verificationId: null,
      };

      // 如果已验证，尝试获取验证记录ID
      if (verificationInfo.isVerified) {
        try {
          const verification = await this.verificationModel
            .findOne({
              productNumber,
              verificationResult: "success",
            })
            .sort({ verificationTime: -1 }) // 获取最新的验证记录
            .exec();

          if (verification) {
            verificationInfo.verificationId = verification._id.toString();
          }
        } catch (verificationError) {
          this.logger.warn(
            `Failed to get verification info for product ${productNumber}:`,
            verificationError
          );
          // 验证信息获取失败不影响商品信息返回
        }
      }

      // 3. 格式化返回数据
      const productInfo = {
        productNumber: product.productNumber,
        name: product.name,
        alcoholContent: product.alcoholContent,
        packagingDate: DateFormatterUtil.formatDateTime(product.packagingDate),
        brand: product.brand,
        category: product.category,
        volume: product.volume,
        description: product.description,
        status: product.status,
        verificationStatus: product.verificationStatus,
        batchNumber: product.batchNumber,
        productionLocation: product.productionLocation,
        verifiedAt: product.verifiedAt
          ? DateFormatterUtil.formatDateTime(product.verifiedAt)
          : null,
        createdAt: DateFormatterUtil.formatDateTime(product.createdAt),
        updatedAt: DateFormatterUtil.formatDateTime(product.updatedAt),
      };

      return {
        success: true,
        data: {
          product: productInfo,
          verification: verificationInfo,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }

      this.logger.error(
        `Failed to get product detail for ${productNumber}:`,
        error
      );
      throw new Error("获取商品详情失败");
    }
  }
}

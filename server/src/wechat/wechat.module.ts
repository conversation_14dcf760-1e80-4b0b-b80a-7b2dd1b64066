import { Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { WechatService } from "./wechat.service";
import { WechatController } from "./wechat.controller";
import { ProductModule } from "../product/product.module";
import { HttpModule } from "@nestjs/axios";
import { CacheModule } from "@nestjs/cache-manager";
import { Product, ProductSchema } from "../product/schemas/product.schema";
import {
  Verification,
  VerificationSchema,
} from "../verification/schemas/verification.schema";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Product.name, schema: ProductSchema },
      { name: Verification.name, schema: VerificationSchema },
    ]),
    ProductModule,
    HttpModule,
    CacheModule.register({
      ttl: 600, // 默认缓存时间（秒）
      max: 100,
    }),
  ],
  controllers: [WechatController],
  providers: [WechatService],
  exports: [WechatService],
})
export class WechatModule {}

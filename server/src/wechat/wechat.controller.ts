import { <PERSON>, <PERSON>, Query, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "@nestjs/common";
import { WechatService } from "./wechat.service";
import { Response } from "express";
import {
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
  ApiParam,
} from "@nestjs/swagger";

@ApiTags("微信二维码")
@Controller("wechat")
export class WechatController {
  constructor(private readonly wechatService: WechatService) {}

  @Get("qrcode")
  @Header("Content-Type", "image/png")
  @ApiOperation({
    summary: "生成小程序二维码",
    description: "返回PNG格式的二维码图片",
  })
  @ApiQuery({
    name: "page",
    required: true,
    description: "小程序页面路径，例如：pages/index/index",
    example: "pages/index/index",
  })
  @ApiQuery({
    name: "scene",
    required: false,
    description: "参数要放在这里",
    example: "productNumber=22108061",
  })
  // @ApiQ<PERSON><PERSON>({
  //   name: "check_path",
  //   required: false,
  //   description:
  //     "为 true 时 page 必须是已经发布的小程序存在的页面（否则报错）；为 false 时允许小程序未发布或者 page 不存在",
  //   example: true,
  // })
  @ApiQuery({
    name: "env_version",
    required: false,
    description: '正式版为 "release"，体验版为 "trial"，开发版为 "develop"',
    example: "develop",
  })
  @ApiResponse({ status: 200, description: "PNG格式图片" })
  @ApiResponse({ status: 400, description: "参数错误" })
  @ApiResponse({ status: 500, description: "服务器内部错误" })
  async getQrcode(
    @Query("page") page: string,
    @Query("scene") scene: string,
    // @Query("env_version") check_path = true,
    @Query("env_version") env_version = "develop",
    @Res() res: Response
  ) {
    if (!page) {
      return res.status(400).json({ message: "路径参数不能为空" });
    }

    try {
      const qrcode = await this.wechatService.generateWxacode({
        page,
        scene,
        check_path: true,
        env_version,
      });

      res.send(qrcode);
    } catch (error) {
      if (error.message === "QRCODE_GENERATION_FAILED") {
        return res.status(500).json({ message: "二维码生成失败" });
      }
      res.status(500).json({ message: "服务器内部错误" });
    }
  }

  @Get("product/:productNumber")
  @ApiOperation({
    summary: "获取商品详情（小程序端）",
    description: "根据商品编号获取商品信息和验证状态",
  })
  @ApiParam({
    name: "productNumber",
    required: true,
    description: "商品编号（8位数字）",
    example: "12345678",
  })
  @ApiResponse({
    status: 200,
    description: "获取成功",
    schema: {
      type: "object",
      properties: {
        success: { type: "boolean", example: true },
        data: {
          type: "object",
          properties: {
            product: {
              type: "object",
              properties: {
                productNumber: { type: "string", example: "12345678" },
                name: { type: "string", example: "茅台酒" },
                alcoholContent: { type: "number", example: 53 },
                packagingDate: {
                  type: "string",
                  example: "2024-01-15T00:00:00.000Z",
                },
                brand: { type: "string", example: "茅台" },
                category: { type: "string", example: "白酒" },
                volume: { type: "number", example: 500 },
                description: { type: "string", example: "优质白酒" },
                status: { type: "string", example: "active" },
                batchNumber: { type: "string", example: "B20240115001" },
                productionLocation: { type: "string", example: "贵州茅台镇" },
              },
            },
            verification: {
              type: "object",
              properties: {
                isVerified: { type: "boolean", example: true },
                verificationTime: {
                  type: "string",
                  example: "2024-01-20T10:30:00.000Z",
                },
                verifiedBy: { type: "string", example: "13800138000" },
                verificationId: {
                  type: "string",
                  example: "60f7b3b3b3b3b3b3b3b3b3b3",
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: "商品不存在" })
  @ApiResponse({ status: 500, description: "服务器内部错误" })
  async getProductDetail(@Param("productNumber") productNumber: string) {
    return this.wechatService.getProductDetail(productNumber);
  }
}

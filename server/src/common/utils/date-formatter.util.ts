import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

/**
 * 日期格式化工具类
 */
export class DateFormatterUtil {
  /**
   * 默认日期时间格式：YYYY-MM-DD HH:mm:ss
   */
  private static readonly DEFAULT_DATETIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';

  /**
   * 默认日期格式：YYYY-MM-DD
   */
  private static readonly DEFAULT_DATE_FORMAT = 'yyyy-MM-dd';

  /**
   * 格式化日期时间为 YYYY-MM-DD HH:mm:ss 格式
   * @param date 日期对象或日期字符串
   * @returns 格式化后的日期时间字符串
   */
  static formatDateTime(date: Date | string | null | undefined): string | null {
    if (!date) return null;
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return null;
      
      return format(dateObj, this.DEFAULT_DATETIME_FORMAT, { locale: zhCN });
    } catch (error) {
      console.error('Date formatting error:', error);
      return null;
    }
  }

  /**
   * 格式化日期为 YYYY-MM-DD 格式
   * @param date 日期对象或日期字符串
   * @returns 格式化后的日期字符串
   */
  static formatDate(date: Date | string | null | undefined): string | null {
    if (!date) return null;
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return null;
      
      return format(dateObj, this.DEFAULT_DATE_FORMAT, { locale: zhCN });
    } catch (error) {
      console.error('Date formatting error:', error);
      return null;
    }
  }

  /**
   * 自定义格式化日期
   * @param date 日期对象或日期字符串
   * @param formatStr 格式字符串
   * @returns 格式化后的日期字符串
   */
  static formatCustom(date: Date | string | null | undefined, formatStr: string): string | null {
    if (!date) return null;
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      if (isNaN(dateObj.getTime())) return null;
      
      return format(dateObj, formatStr, { locale: zhCN });
    } catch (error) {
      console.error('Date formatting error:', error);
      return null;
    }
  }

  /**
   * 格式化对象中的日期字段
   * @param obj 包含日期字段的对象
   * @param dateFields 需要格式化的日期字段名数组
   * @param formatType 格式化类型：'datetime' | 'date' | 'custom'
   * @param customFormat 自定义格式字符串（当 formatType 为 'custom' 时使用）
   * @returns 格式化后的对象
   */
  static formatObjectDates<T extends Record<string, any>>(
    obj: T,
    dateFields: (keyof T)[],
    formatType: 'datetime' | 'date' | 'custom' = 'datetime',
    customFormat?: string
  ): T {
    if (!obj) return obj;

    const result = { ...obj };

    dateFields.forEach(field => {
      if (result[field]) {
        switch (formatType) {
          case 'date':
            result[field] = this.formatDate(result[field] as any) as any;
            break;
          case 'custom':
            if (customFormat) {
              result[field] = this.formatCustom(result[field] as any, customFormat) as any;
            }
            break;
          case 'datetime':
          default:
            result[field] = this.formatDateTime(result[field] as any) as any;
            break;
        }
      }
    });

    return result;
  }

  /**
   * 格式化数组中对象的日期字段
   * @param array 对象数组
   * @param dateFields 需要格式化的日期字段名数组
   * @param formatType 格式化类型
   * @param customFormat 自定义格式字符串
   * @returns 格式化后的数组
   */
  static formatArrayDates<T extends Record<string, any>>(
    array: T[],
    dateFields: (keyof T)[],
    formatType: 'datetime' | 'date' | 'custom' = 'datetime',
    customFormat?: string
  ): T[] {
    if (!Array.isArray(array)) return array;

    return array.map(item => 
      this.formatObjectDates(item, dateFields, formatType, customFormat)
    );
  }

  /**
   * 检查日期是否有效
   * @param date 日期对象或日期字符串
   * @returns 是否为有效日期
   */
  static isValidDate(date: Date | string | null | undefined): boolean {
    if (!date) return false;
    
    try {
      const dateObj = typeof date === 'string' ? new Date(date) : date;
      return !isNaN(dateObj.getTime());
    } catch {
      return false;
    }
  }

  /**
   * 获取当前时间的格式化字符串
   * @param formatType 格式化类型
   * @param customFormat 自定义格式字符串
   * @returns 格式化后的当前时间字符串
   */
  static getCurrentFormatted(
    formatType: 'datetime' | 'date' | 'custom' = 'datetime',
    customFormat?: string
  ): string {
    const now = new Date();
    
    switch (formatType) {
      case 'date':
        return this.formatDate(now) || '';
      case 'custom':
        return customFormat ? this.formatCustom(now, customFormat) || '' : '';
      case 'datetime':
      default:
        return this.formatDateTime(now) || '';
    }
  }
}

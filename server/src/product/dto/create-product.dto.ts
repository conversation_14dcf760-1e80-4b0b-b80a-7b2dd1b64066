import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsNumber,
  IsDateString,
  IsOptional,
  IsE<PERSON>,
  <PERSON>,
  <PERSON>,
} from "class-validator";

export class CreateProductDto {
  @ApiProperty({ description: "商品名称", example: "茅台酒" })
  @IsString()
  name: string;

  @ApiProperty({ description: "酒精度", example: 53, minimum: 0, maximum: 100 })
  @IsNumber()
  @Min(0)
  @Max(100)
  alcoholContent: number;

  @ApiProperty({ description: "包装日期", example: "2024-01-15T00:00:00.000Z" })
  @IsDateString()
  packagingDate: string;

  @ApiProperty({
    description: "商品描述",
    required: false,
    example: "优质白酒",
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: "商品状态",
    enum: ["active", "inactive", "discontinued"],
    default: "active",
    required: false,
  })
  @IsOptional()
  @IsEnum(["active", "inactive", "discontinued"])
  status?: string;

  @ApiProperty({
    description: "验证状态",
    enum: ["unverified", "verified", "expired"],
    default: "unverified",
    required: false,
  })
  @IsOptional()
  @IsEnum(["unverified", "verified", "expired"])
  verificationStatus?: string;

  @ApiProperty({ description: "品牌", required: false, example: "茅台" })
  @IsOptional()
  @IsString()
  brand?: string;

  @ApiProperty({ description: "分类", required: false, example: "白酒" })
  @IsOptional()
  @IsString()
  category?: string;

  @ApiProperty({ description: "容量（毫升）", required: false, example: 500 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  volume?: number;

  @ApiProperty({
    description: "批次号",
    required: false,
    example: "B20240115001",
  })
  @IsOptional()
  @IsString()
  batchNumber?: string;

  @ApiProperty({
    description: "生产地",
    required: false,
    example: "贵州茅台镇",
  })
  @IsOptional()
  @IsString()
  productionLocation?: string;
}

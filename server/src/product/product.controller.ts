import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { ProductService } from "./product.service";
import { CreateProductDto } from "./dto/create-product.dto";
import { UpdateProductDto } from "./dto/update-product.dto";
import { QueryProductDto } from "./dto/query-product.dto";

@ApiTags("商品管理")
@Controller("products")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post()
  @ApiOperation({ summary: "创建商品" })
  @ApiResponse({ status: 201, description: "创建成功" })
  @ApiResponse({ status: 400, description: "请求参数错误" })
  @ApiResponse({ status: 409, description: "商品编号冲突" })
  create(@Body() createProductDto: CreateProductDto) {
    return this.productService.create(createProductDto);
  }

  @Get()
  @ApiOperation({ summary: "获取商品列表" })
  @ApiResponse({ status: 200, description: "获取成功" })
  findAll(@Query() queryDto: QueryProductDto) {
    return this.productService.findAll(queryDto);
  }

  @Get("stats")
  @ApiOperation({ summary: "获取商品统计信息" })
  @ApiResponse({ status: 200, description: "获取成功" })
  getStats() {
    return this.productService.getStats();
  }

  @Get("brands")
  @ApiOperation({ summary: "获取所有品牌" })
  @ApiResponse({ status: 200, description: "获取成功" })
  getBrands() {
    return this.productService.getBrands();
  }

  @Get("categories")
  @ApiOperation({ summary: "获取所有分类" })
  @ApiResponse({ status: 200, description: "获取成功" })
  getCategories() {
    return this.productService.getCategories();
  }

  @Get(":id")
  @ApiOperation({ summary: "获取商品详情" })
  @ApiParam({ name: "id", description: "商品ID" })
  @ApiResponse({ status: 200, description: "获取成功" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  findOne(@Param("id") id: string) {
    return this.productService.findOne(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "更新商品" })
  @ApiParam({ name: "id", description: "商品ID" })
  @ApiResponse({ status: 200, description: "更新成功" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  update(@Param("id") id: string, @Body() updateProductDto: UpdateProductDto) {
    return this.productService.update(id, updateProductDto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "删除商品" })
  @ApiParam({ name: "id", description: "商品ID" })
  @ApiResponse({ status: 200, description: "删除成功" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  @HttpCode(HttpStatus.OK)
  remove(@Param("id") id: string) {
    return this.productService.remove(id);
  }

  @Get("number/:productNumber")
  @ApiOperation({ summary: "根据商品编号获取商品信息" })
  @ApiParam({ name: "productNumber", description: "商品编号" })
  @ApiResponse({ status: 200, description: "获取成功" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  findByProductNumber(@Param("productNumber") productNumber: string) {
    return this.productService.findByProductNumber(productNumber);
  }
}

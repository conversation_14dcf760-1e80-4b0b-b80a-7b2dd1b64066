import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import {
  Verification,
  VerificationDocument,
} from "./schemas/verification.schema";
import { Product, ProductDocument } from "../product/schemas/product.schema";
import {
  VerifyProductDto,
  QueryVerificationDto,
} from "./dto/verify-product.dto";
import { DateFormatterUtil } from "../common/utils/date-formatter.util";

@Injectable()
export class VerificationService {
  constructor(
    @InjectModel(Verification.name)
    private verificationModel: Model<VerificationDocument>,
    @InjectModel(Product.name) private productModel: Model<ProductDocument>
  ) {}

  /**
   * 格式化验证记录的日期字段
   * @param verification 验证记录对象
   * @returns 格式化后的验证记录对象
   */
  private formatVerificationDates(verification: any): any {
    if (!verification) return verification;

    const dateFields = ["verificationTime", "createdAt", "updatedAt"];

    // 如果是 Mongoose 文档，先转换为普通对象
    const verificationObj = verification.toObject
      ? verification.toObject()
      : verification;

    return DateFormatterUtil.formatObjectDates(
      verificationObj,
      dateFields,
      "datetime"
    );
  }

  /**
   * 格式化验证记录数组的日期字段
   * @param verifications 验证记录数组
   * @returns 格式化后的验证记录数组
   */
  private formatVerificationArrayDates(verifications: any[]): any[] {
    if (!Array.isArray(verifications)) return verifications;

    return verifications.map((verification) =>
      this.formatVerificationDates(verification)
    );
  }

  /**
   * 验证商品二维码
   * @param verifyDto 验证数据
   * @returns 验证结果
   */
  async verifyProduct(verifyDto: VerifyProductDto) {
    const { productNumber } = verifyDto;

    // 1. 查找商品
    const product = await this.productModel.findOne({ productNumber }).exec();
    if (!product) {
      throw new NotFoundException("商品不存在");
    }

    // 2. 检查商品状态
    if (product.status !== "active") {
      throw new BadRequestException("商品状态异常，无法验证");
    }

    // 3. 检查是否已经被验证过 - 通过 verifiedAt 是否有值判断
    if (product.verifiedAt) {
      // 记录重复验证尝试
      await this.createVerificationRecord({
        productNumber,
        productId: product._id,
        verificationResult: "duplicate",
        remarks: `商品已被验证过`,
      });

      throw new ConflictException(
        `商品已被验证，验证时间：${DateFormatterUtil.formatDateTime(product.verifiedAt)}`
      );
    }

    try {
      // 4. 更新商品验证状态
      const updatedProduct = await this.productModel
        .findByIdAndUpdate(
          product._id,
          {
            verificationStatus: "verified", // 仍然更新状态字段保持兼容
            verifiedAt: new Date(),
            updatedAt: new Date(),
          },
          { new: true }
        )
        .exec();

      // 5. 创建验证记录
      const verification = await this.createVerificationRecord({
        productNumber,
        productId: product._id,
        verificationResult: "success",
      });

      return {
        success: true,
        message: "验证成功",
        data: {
          productNumber,
          productName: product.name,
          verificationTime: DateFormatterUtil.formatDateTime(
            verification.verificationTime
          ),
          verificationId: (verification as any)._id,
          product: {
            name: updatedProduct.name,
            brand: updatedProduct.brand,
            alcoholContent: updatedProduct.alcoholContent,
            packagingDate: DateFormatterUtil.formatDateTime(
              updatedProduct.packagingDate
            ),
            batchNumber: updatedProduct.batchNumber,
            productionLocation: updatedProduct.productionLocation,
          },
        },
      };
    } catch (error) {
      // 验证失败，记录失败记录
      await this.createVerificationRecord({
        productNumber,
        productId: product._id,
        verificationResult: "failed",
        remarks: `验证失败：${error.message}`,
      });

      throw error;
    }
  }

  /**
   * 创建验证记录
   * @param data 验证数据
   * @returns 验证记录
   */
  private async createVerificationRecord(data: any): Promise<Verification> {
    const verification = new this.verificationModel({
      productNumber: data.productNumber,
      productId: data.productId,
      phoneNumber: data.phoneNumber || null,
      userName: data.userName,
      wechatOpenId: data.wechatOpenId,
      wechatUnionId: data.wechatUnionId,
      verificationTime: new Date(),
      location: data.location,
      deviceInfo: data.deviceInfo,
      verificationType: data.verificationType || "qrcode",
      verificationResult: data.verificationResult || "success",
      remarks: data.remarks,
    });

    return verification.save();
  }

  /**
   * 获取验证记录列表
   * @param queryDto 查询参数
   * @returns 验证记录列表
   */
  async getVerifications(queryDto: QueryVerificationDto) {
    const {
      page,
      limit,
      productNumber,
      verificationResult,
      verificationType,
      sortBy,
      sortOrder,
    } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {};

    if (productNumber) {
      query.productNumber = { $regex: productNumber, $options: "i" };
    }

    if (verificationResult) {
      query.verificationResult = verificationResult;
    }

    if (verificationType) {
      query.verificationType = verificationType;
    }

    // 构建排序条件
    const sort: any = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    const [data, total] = await Promise.all([
      this.verificationModel
        .find(query)
        .populate("productId", "name brand alcoholContent packagingDate")
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.verificationModel.countDocuments(query).exec(),
    ]);

    return {
      data: this.formatVerificationArrayDates(data),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 根据商品编号获取验证记录
   * @param productNumber 商品编号
   * @returns 验证记录
   */
  async getVerificationByProductNumber(productNumber: string) {
    const verification = await this.verificationModel
      .findOne({ productNumber, verificationResult: "success" })
      .populate("productId", "name brand alcoholContent packagingDate")
      .exec();

    if (!verification) {
      throw new NotFoundException("未找到验证记录");
    }

    return this.formatVerificationDates(verification);
  }

  /**
   * 获取验证统计信息
   * @returns 统计信息
   */
  async getVerificationStats() {
    const [
      totalVerifications,
      successVerifications,
      failedVerifications,
      duplicateVerifications,
    ] = await Promise.all([
      this.verificationModel.countDocuments().exec(),
      this.verificationModel
        .countDocuments({ verificationResult: "success" })
        .exec(),
      this.verificationModel
        .countDocuments({ verificationResult: "failed" })
        .exec(),
      this.verificationModel
        .countDocuments({ verificationResult: "duplicate" })
        .exec(),
    ]);

    // 获取今日验证数量
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayVerifications = await this.verificationModel
      .countDocuments({
        verificationTime: { $gte: today },
        verificationResult: "success",
      })
      .exec();

    // 获取本月验证数量
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);
    const monthlyVerifications = await this.verificationModel
      .countDocuments({
        verificationTime: { $gte: thisMonth },
        verificationResult: "success",
      })
      .exec();

    return {
      total: totalVerifications,
      success: successVerifications,
      failed: failedVerifications,
      duplicate: duplicateVerifications,
      today: todayVerifications,
      thisMonth: monthlyVerifications,
      successRate:
        totalVerifications > 0
          ? ((successVerifications / totalVerifications) * 100).toFixed(2)
          : "0.00",
    };
  }

  /**
   * 批量验证商品
   * @param verifications 批量验证数据
   * @returns 批量验证结果
   */
  async batchVerifyProducts(verifications: VerifyProductDto[]) {
    const results = [];
    const errors = [];

    for (const verifyDto of verifications) {
      try {
        const result = await this.verifyProduct({
          ...verifyDto,
          verificationType: "batch",
        });
        results.push(result);
      } catch (error) {
        errors.push({
          productNumber: verifyDto.productNumber,
          error: error.message,
        });
      }
    }

    return {
      success: true,
      data: {
        results,
        errors,
        total: verifications.length,
        successCount: results.length,
        errorCount: errors.length,
      },
    };
  }
}

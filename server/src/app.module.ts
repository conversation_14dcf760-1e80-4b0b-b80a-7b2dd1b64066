import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { MongooseModule } from "@nestjs/mongoose";
import { ProductModule } from "./product/product.module";
import { AuthModule } from "./auth/auth.module";
import { WechatModule } from "./wechat/wechat.module";
import { VerificationModule } from "./verification/verification.module";
import { HttpModule } from "@nestjs/axios";
import { CacheModule } from "@nestjs/cache-manager";

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
    }),

    // MongoDB 连接
    MongooseModule.forRoot(
      process.env.MONGODB_URI || "mongodb://192.168.31.20:27017/qr_management"
    ),
    // 业务模块
    AuthModule,
    ProductModule,
    WechatModule,
    VerificationModule,
  ],
})
export class AppModule {}

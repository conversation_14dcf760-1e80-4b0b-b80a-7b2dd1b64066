import { ApiProperty } from '@nestjs/swagger';
import { IsString, MinLength } from 'class-validator';

export class ChangePasswordDto {
  @ApiProperty({ description: '当前密码', example: 'admin' })
  @IsString()
  @MinLength(4, { message: '密码至少4位' })
  currentPassword: string;

  @ApiProperty({ description: '新密码', example: 'newpassword' })
  @IsString()
  @MinLength(4, { message: '密码至少4位' })
  newPassword: string;
}

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  @Prop({ required: true, unique: true })
  username: string; // 用户名

  @Prop({ required: true })
  password: string; // 密码（加密后）

  @Prop({ default: 'admin' })
  role: string; // 角色

  @Prop({ default: true })
  isActive: boolean; // 是否激活

  @Prop()
  lastLoginAt?: Date; // 最后登录时间

  @Prop({ default: Date.now })
  createdAt: Date; // 创建时间

  @Prop({ default: Date.now })
  updatedAt: Date; // 更新时间
}

export const UserSchema = SchemaFactory.createForClass(User);

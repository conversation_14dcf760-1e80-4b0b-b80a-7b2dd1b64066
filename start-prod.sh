#!/bin/bash

echo "🚀 启动二维码管理平台 - 生产环境"
echo "================================"

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (版本 >= 16)"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，请升级到 16 或更高版本"
    exit 1
fi

# 检查 MongoDB 是否运行
if ! pgrep -x "mongod" > /dev/null; then
    echo "❌ MongoDB 未运行，生产环境必须启动 MongoDB 服务"
    exit 1
fi

# 检查 PM2 是否安装
if ! command -v pm2 &> /dev/null; then
    echo "⚠️  PM2 未安装，建议安装 PM2 进行进程管理"
    echo "   npm install -g pm2"
    echo ""
fi

echo "📦 安装后端依赖..."
cd server
if [ ! -d "node_modules" ]; then
    npm ci --only=production
fi

echo "📦 安装前端依赖..."
cd ../admin
if [ ! -d "node_modules" ]; then
    npm ci --only=production
fi

echo "🔧 配置生产环境..."
cd ../server
if [ ! -f ".env" ]; then
    cp .env.production .env
    echo "✅ 已创建生产环境配置文件"
    echo "⚠️  请立即修改 .env 文件中的敏感配置（JWT_SECRET、数据库密码等）"
else
    echo "✅ 生产环境配置文件已存在"
fi

echo "🏗️  构建应用..."
cd ../server
npm run build:prod

cd ../admin
npm run build

echo ""
echo "🎉 生产环境构建完成！"
echo ""
echo "启动服务："
echo "方式一 - 直接启动:"
echo "  cd server && npm run start:prod"
echo ""
echo "方式二 - 使用 PM2 (推荐):"
echo "  pm2 start server/dist/main.js --name qr-backend"
echo "  pm2 startup"
echo "  pm2 save"
echo ""
echo "前端部署："
echo "  将 admin/build 目录部署到 Nginx 或其他 Web 服务器"
echo ""
echo "环境信息："
echo "- 环境: 生产环境 (production)"
echo "- 数据库: qr_management_prod"
echo "- 调试模式: 关闭"
echo "- API 文档: 关闭（安全考虑）"
echo ""
echo "🔒 安全检查清单："
echo "- [ ] 修改默认的 JWT_SECRET"
echo "- [ ] 配置 HTTPS"
echo "- [ ] 设置防火墙规则"
echo "- [ ] 配置日志轮转"
echo "- [ ] 设置监控和告警"
echo "- [ ] 备份数据库"

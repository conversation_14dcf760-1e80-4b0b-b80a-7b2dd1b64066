#!/bin/bash

echo "🚀 二维码管理平台启动器"
echo "======================"
echo ""
echo "请选择启动环境："
echo "1) 开发环境 (development)"
echo "2) 预发环境 (staging)"
echo "3) 生产环境 (production)"
echo "4) 快速开发启动 (默认)"
echo ""
read -p "请输入选项 (1-4, 默认: 4): " choice

case $choice in
    1)
        echo "启动开发环境..."
        ./start-dev.sh
        exit 0
        ;;
    2)
        echo "启动预发环境..."
        ./start-staging.sh
        exit 0
        ;;
    3)
        echo "启动生产环境..."
        ./start-prod.sh
        exit 0
        ;;
    4|"")
        echo "快速开发启动..."
        ;;
    *)
        echo "无效选项，使用默认开发环境"
        ;;
esac

echo ""
echo "🚀 快速开发环境启动"
echo "=================="

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (版本 >= 16)"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，请升级到 16 或更高版本"
    exit 1
fi

# 检查 MongoDB 是否运行 不再检查DB 使用远程数据库
# if ! pgrep -x "mongod" > /dev/null; then
#     echo "⚠️  MongoDB 未运行，请先启动 MongoDB 服务"
#     echo "   macOS: brew services start mongodb-community"
#     echo "   Ubuntu: sudo systemctl start mongod"
#     echo "   Windows: net start MongoDB"
#     echo ""
# fi

echo "📦 安装后端依赖..."
cd server
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "📦 安装前端依赖..."
cd ../admin
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "🔧 复制环境配置文件..."
cd ../server
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ 已创建 server/.env 文件，请根据需要修改配置"
fi

echo ""
echo "🎉 准备完成！"
echo ""
echo "启动服务："
echo "1. 后端服务: cd server && npm run start:dev"
echo "2. 前端服务: cd admin && npm start"
echo ""

# 在最后的提示信息后添加以下内容（替换原来的手动启动提示部分）

echo ""
echo "🚀 正在启动服务..."
echo "=================="
echo "访问地址："
echo "- 前端管理平台: http://localhost:3001"
echo "- 后端 API: http://localhost:3000"
echo "- API 文档: http://localhost:3000/api"


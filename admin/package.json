{"name": "qr-management-admin", "version": "1.0.0", "description": "二维码管理平台前端", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "antd": "^5.8.4", "axios": "^1.4.0", "dayjs": "^1.11.9", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "typescript": "^4.7.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "cross-env REACT_APP_ENV=development react-scripts start", "start:staging": "cross-env REACT_APP_ENV=staging react-scripts start", "build": "cross-env REACT_APP_ENV=production react-scripts build", "build:staging": "cross-env REACT_APP_ENV=staging react-scripts build", "build:dev": "cross-env REACT_APP_ENV=development react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "cross-env": "^7.0.3"}, "proxy": "http://localhost:3000"}
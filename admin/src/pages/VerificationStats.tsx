import React, { useEffect, useState } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  message,
  Spin,
} from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  CalendarOutlined,
} from "@ant-design/icons";
import { verificationApi, VerificationStats } from "../services/api";

const VerificationStatsPage: React.FC = () => {
  const [stats, setStats] = useState<VerificationStats | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await verificationApi.getStats();
      setStats(response);
    } catch (error) {
      message.error("获取统计信息失败");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "100px 0" }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  const successRate = parseFloat(stats.successRate);

  return (
    <div>
      <Card title="验证统计概览" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="总验证次数"
                value={stats.total}
                prefix={<BarChartOutlined />}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="成功验证"
                value={stats.success}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="失败验证"
                value={stats.failed}
                prefix={<CloseCircleOutlined />}
                valueStyle={{ color: "#ff4d4f" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="重复验证"
                value={stats.duplicate}
                prefix={<ExclamationCircleOutlined />}
                valueStyle={{ color: "#faad14" }}
              />
            </Card>
          </Col>
        </Row>
      </Card>

      <Row gutter={[16, 16]}>
        <Col xs={24} md={12}>
          <Card title="验证成功率">
            <div style={{ textAlign: "center" }}>
              <Progress
                type="circle"
                percent={successRate}
                format={(percent) => `${percent}%`}
                strokeColor={{
                  "0%": "#108ee9",
                  "100%": "#87d068",
                }}
                size={200}
              />
              <div style={{ marginTop: 16, fontSize: 16 }}>
                成功率：{stats.successRate}%
              </div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={12}>
          <Card title="时间统计">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="今日验证"
                    value={stats.today}
                    prefix={<CalendarOutlined />}
                    valueStyle={{ color: "#1890ff" }}
                  />
                </Card>
              </Col>
              <Col span={24}>
                <Card>
                  <Statistic
                    title="本月验证"
                    value={stats.thisMonth}
                    prefix={<CalendarOutlined />}
                    valueStyle={{ color: "#52c41a" }}
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      <Card title="验证结果分布" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: "center" }}>
              <div style={{ fontSize: 18, marginBottom: 8 }}>成功验证</div>
              <Progress
                percent={(stats.success / stats.total) * 100}
                strokeColor="#52c41a"
                showInfo={false}
              />
              <div style={{ marginTop: 8 }}>
                {stats.success} / {stats.total}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: "center" }}>
              <div style={{ fontSize: 18, marginBottom: 8 }}>失败验证</div>
              <Progress
                percent={(stats.failed / stats.total) * 100}
                strokeColor="#ff4d4f"
                showInfo={false}
              />
              <div style={{ marginTop: 8 }}>
                {stats.failed} / {stats.total}
              </div>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <div style={{ textAlign: "center" }}>
              <div style={{ fontSize: 18, marginBottom: 8 }}>重复验证</div>
              <Progress
                percent={(stats.duplicate / stats.total) * 100}
                strokeColor="#faad14"
                showInfo={false}
              />
              <div style={{ marginTop: 8 }}>
                {stats.duplicate} / {stats.total}
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      <Card title="数据说明" style={{ marginTop: 24 }}>
        <ul>
          <li><strong>总验证次数</strong>：包括所有验证尝试，无论成功或失败</li>
          <li><strong>成功验证</strong>：商品首次验证成功的次数</li>
          <li><strong>失败验证</strong>：由于商品不存在、状态异常等原因导致的验证失败</li>
          <li><strong>重复验证</strong>：对已验证商品的重复验证尝试</li>
          <li><strong>验证成功率</strong>：成功验证次数占总验证次数的百分比</li>
          <li><strong>今日验证</strong>：今天成功验证的商品数量</li>
          <li><strong>本月验证</strong>：本月成功验证的商品数量</li>
        </ul>
      </Card>
    </div>
  );
};

export default VerificationStatsPage;

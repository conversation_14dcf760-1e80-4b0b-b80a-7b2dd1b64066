import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Typography,
  Space,
  message,
  Divider,
} from 'antd';
import { LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { authApi, ChangePasswordData } from '../services/api';

const { Title, Text } = Typography;

const ChangePassword: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleSubmit = async (values: ChangePasswordData) => {
    try {
      setLoading(true);
      await authApi.changePassword(values);
      message.success('密码更改成功');
      form.resetFields();
      // 可以选择是否跳转回主页
      // navigate('/');
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '密码更改失败';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const validateConfirmPassword = (_: any, value: string) => {
    if (!value || form.getFieldValue('newPassword') === value) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('两次输入的密码不一致'));
  };

  return (
    <div>
      <Card style={{ maxWidth: 600, margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <SafetyOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={2} style={{ marginBottom: 8 }}>
            更改密码
          </Title>
          <Text type="secondary">为了您的账户安全，请定期更改密码</Text>
        </div>

        <Form
          form={form}
          name="changePassword"
          onFinish={handleSubmit}
          autoComplete="off"
          layout="vertical"
          size="large"
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[
              { required: true, message: '请输入当前密码' },
              { min: 4, message: '密码至少4位' },
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入当前密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Divider />

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 4, message: '密码至少4位' },
              {
                pattern: /^(?=.*[a-zA-Z])(?=.*\d).{4,}$/,
                message: '密码必须包含字母和数字',
              },
            ]}
            hasFeedback
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入新密码（至少4位，包含字母和数字）"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              { validator: validateConfirmPassword },
            ]}
            hasFeedback
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入新密码"
              autoComplete="new-password"
            />
          </Form.Item>

          <Form.Item style={{ marginTop: 32 }}>
            <Space style={{ width: '100%', justifyContent: 'center' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                style={{ minWidth: 120 }}
              >
                更改密码
              </Button>
              <Button
                size="large"
                onClick={() => navigate('/')}
                style={{ minWidth: 120 }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>

        <div style={{ marginTop: 24, padding: 16, background: '#f6f8fa', borderRadius: 6 }}>
          <Title level={5}>密码安全提示：</Title>
          <ul style={{ margin: 0, paddingLeft: 20 }}>
            <li>密码长度至少4位</li>
            <li>必须包含字母和数字</li>
            <li>建议定期更改密码</li>
            <li>不要使用过于简单的密码</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default ChangePassword;

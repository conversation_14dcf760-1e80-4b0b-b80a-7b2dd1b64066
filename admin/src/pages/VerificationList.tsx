import React, { useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Card,
  Tooltip,
  message,
  Modal,
  Descriptions,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  verificationApi,
  VerificationRecord,
  VerificationQueryParams,
} from "../services/api";

const { Search } = Input;
const { Option } = Select;

const VerificationList: React.FC = () => {
  const [data, setData] = useState<VerificationRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState<VerificationQueryParams>({});
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] =
    useState<VerificationRecord | null>(null);

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };
      const response = await verificationApi.getVerifications(params);
      setData(response.data);
      setPagination((prev) => ({
        ...prev,
        total: response.total,
      }));
    } catch (error) {
      message.error("获取验证记录失败");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, productNumber: value || undefined }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handlePhoneSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, phoneNumber: value || undefined }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleResultFilter = (value: string) => {
    setFilters((prev: any) => ({
      ...prev,
      verificationResult: value || undefined,
    }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleTypeFilter = (value: string) => {
    setFilters((prev: any) => ({
      ...prev,
      verificationType: value || undefined,
    }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const getResultTag = (result: string) => {
    const resultMap = {
      success: { color: "green", text: "成功", icon: <CheckCircleOutlined /> },
      failed: { color: "red", text: "失败", icon: <CloseCircleOutlined /> },
      duplicate: {
        color: "orange",
        text: "重复",
        icon: <ExclamationCircleOutlined />,
      },
    };
    const config = resultMap[result as keyof typeof resultMap];
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  const getTypeTag = (type: string) => {
    const typeMap = {
      qrcode: { color: "blue", text: "二维码" },
      manual: { color: "purple", text: "手动" },
      batch: { color: "cyan", text: "批量" },
    };
    const config = typeMap[type as keyof typeof typeMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const showDetail = (record: VerificationRecord) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  const columns = [
    {
      title: "商品编号",
      dataIndex: "productNumber",
      key: "productNumber",
      width: 120,
      fixed: "left" as const,
      render: (productNumber: string) => (
        <span style={{ fontFamily: "monospace", fontWeight: "bold" }}>
          {productNumber}
        </span>
      ),
    },
    {
      title: "商品名称",
      dataIndex: ["productId", "name"],
      key: "productName",
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (name: string) => (
        <Tooltip placement="topLeft" title={name}>
          {name || "-"}
        </Tooltip>
      ),
    },
    {
      title: "验证人手机号",
      dataIndex: "phoneNumber",
      key: "phoneNumber",
      width: 130,
      render: (phoneNumber: string) => (
        <span style={{ fontFamily: "monospace" }}>{phoneNumber}</span>
      ),
    },
    {
      title: "验证人姓名",
      dataIndex: "userName",
      key: "userName",
      width: 100,
      render: (userName: string) => userName || "-",
    },
    {
      title: "验证时间",
      dataIndex: "verificationTime",
      key: "verificationTime",
      width: 150,
      render: (date: string) => date || "-",
      sorter: true,
    },
    {
      title: "验证方式",
      dataIndex: "verificationType",
      key: "verificationType",
      width: 100,
      render: (type: string) => getTypeTag(type),
    },
    {
      title: "验证结果",
      dataIndex: "verificationResult",
      key: "verificationResult",
      width: 100,
      render: (result: string) => getResultTag(result),
    },
    {
      title: "验证地点",
      dataIndex: ["location", "address"],
      key: "location",
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (address: string) => (
        <Tooltip placement="topLeft" title={address}>
          {address || "-"}
        </Tooltip>
      ),
    },
    {
      title: "操作",
      key: "action",
      width: 80,
      fixed: "right" as const,
      render: (_: any, record: VerificationRecord) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => showDetail(record)}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div
          style={{
            marginBottom: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <h1 style={{ margin: 0 }}>验证记录管理</h1>
        </div>

        <div
          style={{
            marginBottom: 16,
            display: "flex",
            gap: 16,
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索商品编号"
            allowClear
            style={{ width: 200 }}
            onSearch={handleSearch}
            enterButton={<SearchOutlined />}
          />
          <Search
            placeholder="搜索手机号"
            allowClear
            style={{ width: 200 }}
            onSearch={handlePhoneSearch}
            enterButton={<SearchOutlined />}
          />
          <Select
            placeholder="筛选验证结果"
            allowClear
            style={{ width: 120 }}
            onChange={handleResultFilter}
          >
            <Option value="success">成功</Option>
            <Option value="failed">失败</Option>
            <Option value="duplicate">重复</Option>
          </Select>
          <Select
            placeholder="筛选验证方式"
            allowClear
            style={{ width: 120 }}
            onChange={handleTypeFilter}
          >
            <Option value="qrcode">二维码</Option>
            <Option value="manual">手动</Option>
            <Option value="batch">批量</Option>
          </Select>
          <Button icon={<ReloadOutlined />} onClick={fetchData}>
            刷新
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={data}
          rowKey="_id"
          loading={loading}
          scroll={{ x: "max-content" }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination((prev) => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10,
              }));
            },
          }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="验证记录详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>,
        ]}
        width={800}
      >
        {selectedRecord && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="商品编号" span={2}>
              <span style={{ fontFamily: "monospace", fontWeight: "bold" }}>
                {selectedRecord.productNumber}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="商品名称" span={2}>
              {selectedRecord.productId?.name || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="验证人手机号">
              {selectedRecord.phoneNumber}
            </Descriptions.Item>
            <Descriptions.Item label="验证人姓名">
              {selectedRecord.userName || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="验证时间" span={2}>
              {selectedRecord.verificationTime || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="验证方式">
              {getTypeTag(selectedRecord.verificationType)}
            </Descriptions.Item>
            <Descriptions.Item label="验证结果">
              {getResultTag(selectedRecord.verificationResult)}
            </Descriptions.Item>
            <Descriptions.Item label="验证地点" span={2}>
              {selectedRecord.location?.address || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="设备信息" span={2}>
              {selectedRecord.deviceInfo?.userAgent || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="微信OpenID" span={2}>
              {selectedRecord.wechatOpenId || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="备注" span={2}>
              {selectedRecord.remarks || "-"}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default VerificationList;

import React, { useState } from "react";
import {
  Form,
  Input,
  Button,
  Card,
  Select,
  DatePicker,
  message,
  Space,
  Divider,
  InputNumber,
  Row,
  Col,
} from "antd";
import { useNavigate } from "react-router-dom";
import { ArrowLeftOutlined, SaveOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { productApi, CreateProductData } from "../services/api";

const { TextArea } = Input;
const { Option } = Select;

const CreateProduct: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      const data: CreateProductData = {
        name: values.name,
        alcoholContent: values.alcoholContent,
        packagingDate: values.packagingDate.toISOString(),
        description: values.description,
        status: values.status || "active",
        brand: values.brand,
        category: values.category,
        volume: values.volume,
        batchNumber: values.batchNumber,
        productionLocation: values.productionLocation,
      };

      await productApi.create(data);
      message.success("商品创建成功");
      navigate("/products");
    } catch (error) {
      message.error("创建失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate("/products");
  };

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回列表
            </Button>
            <Divider type="vertical" />
            <h1 style={{ margin: 0 }}>创建商品</h1>
          </Space>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: "active",
            alcoholContent: 0,
          }}
        >
          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                label="商品名称"
                name="name"
                rules={[
                  { required: true, message: "请输入商品名称" },
                  { max: 100, message: "名称不能超过100个字符" },
                ]}
              >
                <Input placeholder="请输入商品名称，如：茅台酒" />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="酒精度 (%)"
                name="alcoholContent"
                rules={[
                  { required: true, message: "请输入酒精度" },
                  { type: "number", min: 0, max: 100, message: "酒精度应在0-100之间" },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入酒精度"
                  min={0}
                  max={100}
                  precision={1}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                label="包装日期"
                name="packagingDate"
                rules={[{ required: true, message: "请选择包装日期" }]}
              >
                <DatePicker
                  style={{ width: "100%" }}
                  placeholder="选择包装日期"
                  disabledDate={(current) =>
                    current && current > dayjs().endOf("day")
                  }
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item label="状态" name="status">
                <Select>
                  <Option value="active">活跃</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="discontinued">停产</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                label="品牌"
                name="brand"
                rules={[{ max: 50, message: "品牌名称不能超过50个字符" }]}
              >
                <Input placeholder="请输入品牌名称，如：茅台" />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="分类"
                name="category"
                rules={[{ max: 50, message: "分类名称不能超过50个字符" }]}
              >
                <Input placeholder="请输入分类，如：白酒" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col xs={24} md={12}>
              <Form.Item
                label="容量 (ml)"
                name="volume"
                rules={[
                  { type: "number", min: 1, message: "容量必须大于0" },
                ]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="请输入容量"
                  min={1}
                  addonAfter="ml"
                />
              </Form.Item>
            </Col>

            <Col xs={24} md={12}>
              <Form.Item
                label="批次号"
                name="batchNumber"
                rules={[{ max: 50, message: "批次号不能超过50个字符" }]}
              >
                <Input placeholder="请输入批次号，如：B20240115001" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="生产地"
            name="productionLocation"
            rules={[{ max: 100, message: "生产地不能超过100个字符" }]}
          >
            <Input placeholder="请输入生产地，如：贵州茅台镇" />
          </Form.Item>

          <Form.Item
            label="商品描述"
            name="description"
            rules={[{ max: 500, message: "描述不能超过500个字符" }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入商品描述（可选）"
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                创建商品
              </Button>
              <Button onClick={handleBack}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default CreateProduct;

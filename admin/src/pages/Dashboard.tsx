import React, { useEffect, useState } from "react";
import { Row, Col, Card, Statistic, Spin, message } from "antd";
import {
  CheckCircleOutlined,
  StopOutlined,
  ClockCircleOutlined,
  ShoppingOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import { productApi, ProductStatsResponse } from "../services/api";

const Dashboard: React.FC = () => {
  const [productStats, setProductStats] = useState<ProductStatsResponse | null>(
    null
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const prodStats = await productApi.getStats();
      setProductStats(prodStats);
    } catch (error) {
      message.error("获取统计信息失败");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <h1>仪表盘</h1>

      {/* 商品统计 */}
      <h2>商品统计</h2>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="总商品数"
              value={productStats?.total || 0}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="活跃商品"
              value={productStats?.active || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="停用商品"
              value={productStats?.inactive || 0}
              prefix={<StopOutlined />}
              valueStyle={{ color: "#faad14" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card className="stats-card">
            <Statistic
              title="停产商品"
              value={productStats?.discontinued || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} sm={12}>
          <Card className="stats-card">
            <Statistic
              title="品牌数量"
              value={productStats?.totalBrands || 0}
              prefix={<TagsOutlined />}
              valueStyle={{ color: "#722ed1" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12}>
          <Card className="stats-card">
            <Statistic
              title="分类数量"
              value={productStats?.totalCategories || 0}
              prefix={<TagsOutlined />}
              valueStyle={{ color: "#eb2f96" }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;

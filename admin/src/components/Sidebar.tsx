import React from "react";
import { Layout, <PERSON>u, Modal } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";
import {
  DashboardOutlined,
  ShoppingOutlined,
  AppstoreAddOutlined,
  LockOutlined,
  LogoutOutlined,
  UserOutlined,
  QrcodeOutlined,
} from "@ant-design/icons";

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout, user } = useAuth();

  const menuItems = [
    {
      key: "/",
      icon: <DashboardOutlined />,
      label: "仪表盘",
    },
    {
      key: "/products",
      icon: <ShoppingOutlined />,
      label: "商品管理",
    },
    {
      key: "/products/create",
      icon: <AppstoreAddOutlined />,
      label: "创建商品",
    },
    {
      key: "/change-password",
      icon: <LockOutlined />,
      label: "更改密码",
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === "logout") {
      Modal.confirm({
        title: "确认退出",
        content: "您确定要退出登录吗？",
        onOk: () => {
          logout();
          navigate("/login");
        },
      });
    } else {
      navigate(key);
    }
  };

  return (
    <Sider width={200} theme="dark">
      <div
        style={{
          height: 64,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "white",
          fontSize: "18px",
          fontWeight: "bold",
        }}
      >
        商品管理平台
      </div>
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        theme="dark"
      />

      {/* 用户信息和退出按钮 */}
      <div
        style={{
          position: "absolute",
          bottom: 0,
          width: "100%",
          borderTop: "1px solid #434343",
          padding: "12px 16px",
          background: "#001529",
        }}
      >
        <div
          style={{
            color: "rgba(255, 255, 255, 0.65)",
            fontSize: "12px",
            marginBottom: "8px",
            display: "flex",
            alignItems: "center",
            gap: "8px",
          }}
        >
          <UserOutlined />
          {user?.username}
        </div>
        <div
          style={{
            color: "#ff4d4f",
            cursor: "pointer",
            fontSize: "12px",
            display: "flex",
            alignItems: "center",
            gap: "8px",
          }}
          onClick={() => handleMenuClick({ key: "logout" })}
        >
          <LogoutOutlined />
          退出登录
        </div>
      </div>
    </Sider>
  );
};

export default Sidebar;

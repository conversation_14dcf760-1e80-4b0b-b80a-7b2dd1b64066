#!/bin/bash

echo "🚀 启动二维码管理平台 - 开发环境"
echo "================================"

# 检查 Node.js 版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (版本 >= 16)"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js 版本过低，请升级到 16 或更高版本"
    exit 1
fi

# 检查 MongoDB 是否运行
if ! pgrep -x "mongod" > /dev/null; then
    echo "⚠️  MongoDB 未运行，请先启动 MongoDB 服务"
    echo "   macOS: brew services start mongodb-community"
    echo "   Ubuntu: sudo systemctl start mongod"
    echo "   Windows: net start MongoDB"
    echo ""
fi

echo "📦 安装后端依赖..."
cd server
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "📦 安装前端依赖..."
cd ../admin
if [ ! -d "node_modules" ]; then
    npm install
fi

echo "🔧 配置开发环境..."
cd ../server
if [ ! -f ".env" ]; then
    cp .env.development .env
    echo "✅ 已创建开发环境配置文件"
fi

echo ""
echo "🎉 开发环境准备完成！"
echo ""
echo "启动服务："
echo "1. 后端服务: cd server && npm run start:dev"
echo "2. 前端服务: cd admin && npm start"
echo ""
echo "访问地址："
echo "- 前端管理平台: http://localhost:3001"
echo "- 后端 API: http://localhost:3000"
echo "- API 文档: http://localhost:3000/api"
echo ""
echo "环境信息："
echo "- 环境: 开发环境 (development)"
echo "- 数据库: qr_management_dev"
echo "- 调试模式: 开启"
